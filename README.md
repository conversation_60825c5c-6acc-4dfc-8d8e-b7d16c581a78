# 广西门户网站前端项目

基于 Vue 3 + TypeScript + Vite 构建的现代化多城市门户网站。

## 🚀 技术栈

- **框架**: Vue 3.4+ with Composition API
- **语言**: TypeScript 5.4+
- **构建工具**: Vite 5.0+
- **UI 库**: Element Plus 2.7+
- **CSS 框架**: UnoCSS 0.61+ (原子化CSS)
- **CSS 预处理**: Sass/SCSS 1.77+
- **状态管理**: Pinia 2.1+ (with persistence)
- **路由管理**: Vue Router 4.3+
- **HTTP 客户端**: Axios (定制封装)
- **测试框架**: Vitest 1.6+ + Vue Test Utils 2.4+

## 📁 项目结构

```
gxjy-portal/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 静态资源(样式、图片等)
│   │   └── styles/        # 全局样式
│   ├── components/        # 通用组件
│   │   ├── common/        # 公共组件
│   │   └── layout/        # 布局组件
│   ├── layout/            # 页面布局
│   ├── router/            # 路由配置
│   ├── services/          # API 服务
│   ├── stores/            # Pinia 状态管理
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面视图
│   │   └── homepages/     # 城市首页
│   │       └── nanning/   # 南宁首页
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── .env.development       # 开发环境变量
├── .env.production        # 生产环境变量
├── .eslintrc.js           # ESLint 配置
├── .gitignore            # Git 忽略文件
├── index.html            # HTML 模板
├── package.json          # 项目依赖配置
├── README.md             # 项目说明
├── tsconfig.json         # TypeScript 配置
├── uno.config.ts         # UnoCSS 配置
└── vite.config.ts        # Vite 构建配置
```

## 🎯 核心特性

### 多城市架构
- **动态路由**: `/:city/page` 结构，支持城市级别的页面定制
- **城市配置**: 通过 Pinia Store 管理城市信息和主题
- **智能切换**: 城市选择器组件，支持无缝切换

### 现代化开发体验
- **TypeScript**: 完整的类型安全保障
- **按需导入**: 自动导入 Vue APIs 和组件
- **热重载**: Vite 提供极速的开发体验
- **代码检查**: ESLint + Prettier 保证代码质量

### 响应式设计
- **移动优先**: 遵循移动优先设计原则
- **断点系统**: 完整的响应式断点配置
- **组件适配**: 所有组件支持多尺寸适配

### 性能优化
- **路由懒加载**: 所有页面组件按需加载
- **资源优化**: 图片懒加载、CSS 原子化
- **缓存策略**: HTTP 缓存和浏览器存储优化
- **代码分割**: 智能的代码分割和打包优化

## 🛠️ 开发指南

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0 或 yarn >= 3.0.0
- 现代浏览器支持 ES2022

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发环境启动

```bash
npm run dev
# 或
yarn dev
```

访问: http://localhost:3000

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 预览生产版本

```bash
npm run preview
# 或
yarn preview
```

### 代码检查和修复

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix

# 类型检查
npm run type-check
```

### 测试

```bash
# 运行测试
npm run test

# 测试 UI 界面
npm run test:ui
```

## 🏗️ 架构设计

### 状态管理
使用 Pinia 进行状态管理，主要包括：
- **cityStore**: 城市信息和配置管理
- **userStore**: 用户认证和个人信息

### 路由设计
采用嵌套路由结构：
- 根路径 `/` 重定向到默认城市
- 城市级路由 `/:city` 作为主要入口
- 页面级路由 `/:city/:page` 对应具体功能

### API 集成
- 统一的 HTTP 客户端封装
- 自动 Token 刷新机制
- 全局错误处理
- 请求/响应拦截器

### 样式系统
- **CSS 变量**: 全局主题配置
- **UnoCSS**: 原子化 CSS 类
- **SCSS**: 复杂样式逻辑
- **Element Plus**: 基础 UI 组件

## 📱 支持的城市

目前支持的城市：
- **南宁**: 完整的首页和服务功能
- **广西**: 基础配置（待完善）

添加新城市需要：
1. 在 `cityStore` 中添加城市配置
2. 创建对应的首页组件: `src/views/homepages/{cityCode}/HomePage.vue`
3. 配置城市特色的服务和链接

## 🚀 部署指南

### 构建配置
项目支持多环境构建：
- **开发环境**: `.env.development`
- **生产环境**: `.env.production`

### 服务器配置
需要配置服务器支持 SPA 路由：

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📋 待办功能

- [ ] 用户认证系统完善
- [ ] 搜索功能实现
- [ ] 更多城市页面
- [ ] PWA 支持
- [ ] 国际化支持
- [ ] 单元测试覆盖
- [ ] E2E 测试
- [ ] 性能监控
- [ ] 错误上报系统

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支: `git checkout -b feature/AmazingFeature`
3. 提交更改: `git commit -m 'Add some AmazingFeature'`
4. 推送到分支: `git push origin feature/AmazingFeature`
5. 开启 Pull Request

### 编码规范
- 遵循 ESLint 配置的代码规范
- 使用 TypeScript 进行类型注解
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case
- 提交信息遵循 Conventional Commits 规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-org/gxjy-portal/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下开源项目：
- [Vue.js](https://vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Element Plus](https://element-plus.org/)
- [UnoCSS](https://unocss.dev/)
- [Pinia](https://pinia.vuejs.org/)