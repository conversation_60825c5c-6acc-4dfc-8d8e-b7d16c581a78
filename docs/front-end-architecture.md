# 广西门户网站前端架构文档

### **版本**: 1.0

### **日期**: 2025年8月12日


## 1. 介绍与范围

本文档定义了广西门户网站项目的前端技术架构、标准和模式。设计目标是创建一个可扩展、高性能、易于维护的现代化Web应用。范围完全聚焦于前端开发，并为未来与自研后端的集成做好了准备。

## 2. 技术栈 (Tech Stack)

| 类别 (Category)   | 技术 (Technology) | 推荐版本 | 目的 (Purpose)                  |
| ----------------- | ----------------- | -------- | ------------------------------- |
| **框架**          | Vue 3             | ^3.4.0   | 核心UI框架                      |
| **构建工具**      | Vite              | ^5.0.0   | 开发服务器与项目构建            |
| **编程语言**      | TypeScript        | ^5.4.0   | 提供静态类型检查                |
| **UI 组件库**     | Element Plus      | ^2.7.0   | 提供高质量的基础UI组件          |
| **原子化CSS**     | UnoCSS            | ^0.61.0  | 按需生成原子化CSS，提升开发效率 |
| **CSS预处理器**   | Sass/SCSS         | ^1.77.0  | 编写更复杂的CSS逻辑和主题       |
| **状态管理**      | Pinia             | ^2.1.0   | Vue 3官方推荐的全局状态管理     |
| **状态管理持久化**      | pinia-plugin-persistedstate             | ^2.1.0   | 全局状态管理持久化    |
| **路由**          | Vue Router        | ^4.3.0   | 官方的客户端路由管理器          |
| **按需自动导入**       | `unplugin-auto-import`/`unplugin-vue-components`           | ^20.0.0 | 按需自动导入           |
| **单元/组件测试** | Vitest            | ^1.6.0   | 用于单元和组件测试的框架        |
| **组件测试库**    | Vue Test Utils    | ^2.4.0   | Vue官方组件测试工具库           |


## 3. 项目结构 (Project Structure)


``` code
/your-project-name
├── public/
├── src/
│   ├── assets/
│   │   └── styles/
│   │       ├── main.scss
│   │       ├── variables.scss
│   │       ├── base.scss
│   │       └── mixins.scss
│   ├── components/
│   │   ├── common/
│   │   └── layout/
│   │       ├── HeaderComponent.vue
│   │       └── CityFooter.vue
│   ├── layout/
│   │   └── AppLayout.vue
│   ├── router/
│   │   └── index.ts
│   ├── services/
│   │   └── api.ts
│   ├── stores/
│   │   └── cityStore.ts
│   ├── types/
│   │   └── index.ts
│   ├── utils/
│   │   └── axios-utils.ts
│   ├── views/
│   │   ├── homepages/
│   │   │   ├── guangxi/
│   │   │   │   └── HomePage.vue
│   │   │   └── nanning/
│   │   │       └── HomePage.vue
│   │   ├── NewsPage.vue
│   │   └── LoginPage.vue
│   ├── App.vue
│   └── main.ts
├── .gitignore
├── index.html
├── package.json
├── [README.md](http://readme.md/)
├── tsconfig.json
├── uno.config.ts         # UnoCSS 配置文件
└── vite.config.ts

```

## 4. 路由与布局 (Routing & Layout)

- **核心方案**: 采用单一主布局 (`AppLayout.vue`) + 动态路由组件解析 + 智能动态页脚 (`CityFooter.vue`)。
- **URL结构**: `/:city/page`，例如 `/nanning/news`。
- **主布局**: `AppLayout.vue` 负责渲染统一的 `Header`，并根据URL参数更新Pinia中的当前城市，然后渲染 `<router-view>` 和智能的 `CityFooter`。
- **城市首页**: 首页路由 (`/:city`) 将根据 `:city` 参数动态加载 `src/views/homepages/[city]/HomePage.vue` 组件，实现每个城市首页的完全定制化。
- **页脚**: `CityFooter.vue` 组件从Pinia store中读取当前城市，并动态渲染该城市专属的页脚内容。
- **性能**: 所有页面级组件均采用路由懒加载。

## 5. 组件标准 (Component Standards)

- **模板**: 所有组件遵循标准的Vue 3 `<script setup lang="ts">` 模板，包含清晰的`props`, `emits`, `state` 定义。
- **命名**: 遵循PascalCase命名法，基础组件以`Base`开头，视图以`View/Page`结尾。

## 6. 状态管理 (State Management)

- **库**: Pinia。
- **模式**: 采用模块化的Setup Store模式，为每个业务领域（如`cityStore`）创建独立的、类型安全的store。

## 7. API 集成 (API Integration)

- **策略**: 使用 `swagger-codegen` 根据OpenAPI规范自动生成类型安全的API客户端代码。
- **核心**: 基于用户提供的 `axios-utils.ts` 建立统一的HTTP请求层，集中处理认证、Token刷新和全局错误。
- **封装**: 在 `src/services/` 目录下对生成的API进行封装，供业务逻辑调用。

## 8. 样式指南 (Styling Guidelines)

- **混合策略**:
  - **Element Plus**: 提供基础复杂组件。
  - **UnoCSS**: 负责所有布局、间距、颜色等原子化样式。
  - **SCSS**: 用于全局样式定义、`scoped`组件内部复杂逻辑和覆盖第三方库样式。
- **主题化**: 在 `src/assets/styles/variables.scss` 中使用CSS自定义属性定义全局设计变量，易于维护和扩展（如深色模式）。

## 9. 测试要求 (Testing Requirements)

- **技术栈**: Vitest + Vue Test Utils + Cypress。
- **策略**: 对关键业务逻辑和组件进行单元/组件测试，对核心用户流程进行E2E测试，目标覆盖率80%。

## 10.资源与性能
- **资源与性能**: 按需自动导入（`unplugin-auto-import`/`unplugin-vue-components`
）、图片优化（`vite-imagetools`）
