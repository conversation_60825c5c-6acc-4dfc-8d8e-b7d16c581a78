# Sass 模块系统迁移指南

## 概述

本项目已成功从旧的 `@import` 规则迁移到新的 Sass 模块系统（`@use` 和 `@forward`）。这是 Sass 推荐的现代化做法，提供了更好的命名空间管理和避免全局污染。

## 文件结构

```
src/assets/styles/
├── index.scss      # 主入口文件，使用 @forward 导出所有模块
├── variables.scss  # CSS 变量和 Sass 变量定义
├── mixins.scss     # Sass mixins 集合
├── base.scss       # 基础样式重置
└── main.scss       # 全局样式定义
```

## 核心变更

### 1. variables.scss
- 添加了 Sass 变量（如 `$breakpoints`）供 mixins 使用
- 保留了所有 CSS 自定义属性（CSS Variables）

### 2. mixins.scss
- 使用 `@use 'sass:map'` 导入 Sass 内置模块
- 使用 `@use './variables' as vars` 导入变量
- 通过命名空间访问变量：`vars.$breakpoints`

### 3. index.scss（新文件）
- 使用 `@forward` 集中导出所有样式模块
- 方便其他文件通过单一入口导入所有样式

### 4. main.scss
- 使用 `@use './index' as *` 导入所有样式模块
- 移除了 UnoCSS 的手动导入（由 Vite 插件自动处理）

## 在 Vue 组件中使用

### 基本用法

```scss
<style lang="scss">
// 使用新的 @use 模块系统
@use '@/assets/styles' as *;

// 现在可以使用所有的 mixins 和变量
.my-component {
  @include mobile-only {
    // 移动端样式
  }
}
</style>
```

### Scoped 样式

```scss
<style scoped lang="scss">
// 即使是 scoped 样式也需要显式导入
@use '@/assets/styles' as *;

.component {
  @include flex-center;
}
</style>
```

## Vite 配置

```typescript
// vite.config.ts
css: {
  preprocessorOptions: {
    scss: {
      // 使用新的 Dart Sass API
      api: 'modern-compiler',
      // 不再需要全局注入
    }
  }
}
```

## 迁移收益

1. **更好的封装性**：每个模块的作用域是独立的，避免了全局命名冲突
2. **明确的依赖关系**：通过 `@use` 明确声明依赖，提高代码可维护性
3. **性能优化**：Sass 只会编译一次被 `@use` 的文件，而 `@import` 会重复编译
4. **面向未来**：`@import` 将在 Dart Sass 3.0 中被弃用

## 注意事项

1. **必须显式导入**：每个需要使用 mixins 或变量的文件都需要显式 `@use`
2. **导入顺序**：`@use` 规则必须在文件最顶部（除了 `@charset` 和 `@forward`）
3. **命名空间**：可以使用 `as *` 来避免命名空间，或使用 `as name` 创建命名空间

## 常见问题

### Q: 为什么每个组件都要导入样式？
A: 这是 Sass 模块系统的设计理念，确保每个文件的依赖关系明确，避免隐式的全局依赖。

### Q: 如何在多个文件中共享变量？
A: 通过 `index.scss` 使用 `@forward` 集中导出，然后在需要的地方 `@use '@/assets/styles' as *`。

### Q: 迁移后编译变慢了？
A: 实际上应该更快，因为 `@use` 只编译一次。如果变慢，可能是配置问题。

## 参考资源

- [Sass: @use](https://sass-lang.com/documentation/at-rules/use)
- [Sass: @forward](https://sass-lang.com/documentation/at-rules/forward)
- [Sass: Module System](https://sass-lang.com/blog/the-module-system-is-launched)
- [Migrating from @import](https://sass-lang.com/documentation/at-rules/import#migrating-from-import)
