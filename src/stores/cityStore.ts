import { defineStore } from 'pinia'
import { ref, computed, watch, onMounted, readonly } from 'vue'
import type { CityInfo, CityCode } from '@/types'

// 城市配置数据
const CITIES_CONFIG: Record<CityCode, CityInfo> = {
  guangxi: {
    code: 'guangxi',
    name: '广西',
    nameEn: 'Guangxi',
    description: '广西壮族自治区门户网站',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/guangxi-logo.png'
    },
    features: ['自治区政务', '招商引资', '文旅服务', '经济发展'],
    isActive: true,
    id: 0
  },
  gl: {
    code: 'gl',
    name: '桂林',
    nameEn: 'Guilin',
    description: '桂林市门户网站，山水甲天下',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/guilin-logo.png'
    },
    features: ['政务服务', '文旅服务', '民生服务', '营商服务'],
    isActive: true,
    id: 1
  },
  lz: {
    code: 'lz',
    name: '柳州',
    nameEn: 'Liuzhou',
    description: '柳州市门户网站，工业重镇',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/liuzhou-logo.png'
    },
    features: ['政务服务', '工业服务', '民生服务', '营商服务'],
    isActive: true,
    id: 2
  },
  wz: {
    code: 'wz',
    name: '梧州',
    nameEn: 'Wuzhou',
    description: '梧州市门户网站，两广枢纽',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/wuzhou-logo.png'
    },
    features: ['政务服务', '两广合作', '民生服务', '营商服务'],
    isActive: true,
    id: 4
  },
  bs: {
    code: 'bs',
    name: '百色',
    nameEn: 'Baise',
    description: '百色市门户网站，革命老区',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/baise-logo.png'
    },
    features: ['政务服务', '红色文旅', '民生服务', '营商服务'],
    isActive: true,
    id: 6
  },
  qz: {
    code: 'qz',
    name: '钦州',
    nameEn: 'Qinzhou',
    description: '钦州市门户网站，北部湾明珠',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/qinzhou-logo.png'
    },
    features: ['政务服务', '港口服务', '民生服务', '营商服务'],
    isActive: true,
    id: 7
  },
  hc: {
    code: 'hc',
    name: '河池',
    nameEn: 'Hechi',
    description: '河池市门户网站，世界长寿之乡',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/hechi-logo.png'
    },
    features: ['政务服务', '长寿文化', '民生服务', '营商服务'],
    isActive: true,
    id: 8
  },
  bh: {
    code: 'bh',
    name: '北海',
    nameEn: 'Beihai',
    description: '北海市门户网站，滨海旅游城市',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/beihai-logo.png'
    },
    features: ['政务服务', '滨海旅游', '民生服务', '营商服务'],
    isActive: true,
    id: 9
  },
  fg: {
    code: 'fg',
    name: '防港',
    nameEn: 'Fangchenggang',
    description: '防城港市门户网站，边境港口城市',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/fanggang-logo.png'
    },
    features: ['政务服务', '边贸服务', '民生服务', '营商服务'],
    isActive: true,
    id: 11
  },
  yl: {
    code: 'yl',
    name: '玉林',
    nameEn: 'Yulin',
    description: '玉林市门户网站，岭南都会',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/yulin-logo.png'
    },
    features: ['政务服务', '商贸服务', '民生服务', '营商服务'],
    isActive: true,
    id: 12
  },
  cz: {
    code: 'cz',
    name: '崇左',
    nameEn: 'Chongzuo',
    description: '崇左市门户网站，边境口岸城市',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/chongzuo-logo.png'
    },
    features: ['政务服务', '边贸服务', '民生服务', '营商服务'],
    isActive: true,
    id: 13
  },
  gg: {
    code: 'gg',
    name: '贵港',
    nameEn: 'Guigang',
    description: '贵港市门户网站，西江明珠',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/guigang-logo.png'
    },
    features: ['政务服务', '港口服务', '民生服务', '营商服务'],
    isActive: true,
    id: 14
  },
  lb: {
    code: 'lb',
    name: '来宾',
    nameEn: 'Laibin',
    description: '来宾市门户网站，世界瑶都',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/laibin-logo.png'
    },
    features: ['政务服务', '瑶族文化', '民生服务', '营商服务'],
    isActive: true,
    id: 15
  },
  hz: {
    code: 'hz',
    name: '贺州',
    nameEn: 'Hezhou',
    description: '贺州市门户网站，粤港澳后花园',
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#10b981',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/hezhou-logo.png'
    },
    features: ['政务服务', '康养旅游', '民生服务', '营商服务'],
    isActive: true,
    id: 18
  },
  nn: {
    code: 'nn',
    name: '南宁',
    nameEn: 'Nanning',
    description: '南宁市，广西壮族自治区首府，北部湾城市群核心城市',
    theme: {
      primaryColor: '#3b82f6',
      secondaryColor: '#059669',
      accentColor: '#f59e0b',
      logoUrl: '/images/logos/nanning-logo.png'
    },
    features: ['政务服务', '民生服务', '营商服务', '便民查询'],
    isActive: true,
    id: 19
  }
}

export const useCityStore = defineStore('city', () => {
  // 状态
  const currentCity = ref<CityCode>('guangxi')
  const cityInfo = ref<CityInfo>(CITIES_CONFIG.guangxi)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const availableCities = computed(() => {
    return Object.values(CITIES_CONFIG).filter(city => city.isActive)
  })

  const cityTheme = computed(() => cityInfo.value.theme)

  const isSupportedCity = computed(() => (cityCode: string): boolean => {
    return cityCode in CITIES_CONFIG
  })

  const getCityByCode = computed(() => (cityCode: CityCode): CityInfo | undefined => {
    return CITIES_CONFIG[cityCode]
  })

  // 统一的路径生成方法
  const getCityHomePath = computed(() => {
    return currentCity.value === 'guangxi' ? '/' : `/${currentCity.value}`
  })

  const getCityPagePath = computed(() => (page: string = '') => {
    if (currentCity.value === 'guangxi') {
      return page ? `/${page}` : '/'
    }
    return page ? `/${currentCity.value}/${page}` : `/${currentCity.value}`
  })

  const isCurrentPath = computed(() => (path: string) => {
    // 这个方法需要在组件中调用，所以暂时保留为函数
    // 实际使用时会传入当前路由路径
    return (currentPath: string) => {
      if (currentCity.value === 'guangxi') {
        if (path === '') {
          return currentPath === '/' || currentPath === ''
        }
        return currentPath.startsWith(path.startsWith('/') ? path : `/${path}`)
      } else {
        const cityPath = `/${currentCity.value}`
        if (path === '') {
          return currentPath === cityPath || currentPath === cityPath + '/'
        }
        return currentPath.startsWith(`${cityPath}${path.startsWith('/') ? path : `/${path}`}`)
      }
    }
  })

  // Actions
  const setCurrentCity = (cityCode: CityCode): boolean => {
    if (!isSupportedCity.value(cityCode)) {
      error.value = `不支持的城市代码: ${cityCode}`
      return false
    }

    try {
      isLoading.value = true
      error.value = null

      const newCityInfo = CITIES_CONFIG[cityCode]
      if (!newCityInfo.isActive) {
        error.value = `城市 ${newCityInfo.name} 暂未开放`
        return false
      }

      currentCity.value = cityCode
      cityInfo.value = newCityInfo

      // 更新页面标题
      document.title = `${newCityInfo.name}门户网站`
      
      // 更新主题色 CSS 变量
      updateThemeColors(newCityInfo.theme)

      console.log(`切换到城市: ${newCityInfo.name}`)
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '切换城市失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const updateThemeColors = (theme: CityInfo['theme']) => {
    const root = document.documentElement
    root.style.setProperty('--color-primary', theme.primaryColor)
    root.style.setProperty('--color-secondary', theme.secondaryColor)
    root.style.setProperty('--color-accent', theme.accentColor)
  }

  const initializeFromRoute = (cityCode: string): boolean => {
    if (!isSupportedCity.value(cityCode)) {
      // 如果路由中的城市不支持，默认跳转到广西
      return setCurrentCity('guangxi')
    }
    return setCurrentCity(cityCode as CityCode)
  }

  const clearError = () => {
    error.value = null
  }

  const refreshCityData = async (): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null
      
      // 这里可以添加从 API 获取城市配置的逻辑
      // const response = await api.getCityConfig(currentCity.value)
      // cityInfo.value = response.data
      
      console.log(`刷新城市数据: ${cityInfo.value.name}`)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刷新数据失败'
    } finally {
      isLoading.value = false
    }
  }

  // 初始化
  const initialize = () => {
    // 从 localStorage 恢复上次选择的城市
    const savedCity = localStorage.getItem('selectedCity') as CityCode
    if (savedCity && isSupportedCity.value(savedCity)) {
      setCurrentCity(savedCity)
    }
  }

  // 监听城市变化，保存到 localStorage
  watch(currentCity, (newCity) => {
    localStorage.setItem('selectedCity', newCity)
  })

  // 初始化城市主题
  onMounted(() => {
    updateThemeColors(cityInfo.value.theme)
  })

  return {
    // State
    currentCity: readonly(currentCity),
    cityInfo: readonly(cityInfo),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    availableCities,
    cityTheme,
    isSupportedCity,
    getCityByCode,

    // 路径生成方法
    getCityHomePath,
    getCityPagePath,
    isCurrentPath,

    // Actions
    setCurrentCity,
    initializeFromRoute,
    clearError,
    refreshCityData,
    initialize
  }
}, {
  persist: {
    key: 'city-store',
    storage: localStorage,
    paths: ['currentCity']
  }
})