// 全局类型定义

// 城市信息类型
export interface CityInfo {
  code: string
  name: string
  nameEn: string
  description: string
  theme: CityTheme
  features: string[]
  isActive: boolean
  id: number // 添加城市ID字段
}

// 城市主题配置
export interface CityTheme {
  primaryColor: string
  secondaryColor: string
  accentColor: string
  backgroundImage?: string
  logoUrl: string
}

// 支持的城市代码
export type CityCode = 'guangxi' | 'gl' | 'lz' | 'wz' | 'bs' | 'qz' | 'hc' | 'bh' | 'fg' | 'yl' | 'cz' | 'gg' | 'lb' | 'hz' | 'nn'

// 路由参数类型
export interface RouteParams {
  city: CityCode
  page?: string
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页数据类型
export interface PaginationData<T = any> {
  list: T[]
  total: number
  current: number
  pageSize: number
}

// 新闻文章类型
export interface NewsArticle {
  id: string
  title: string
  summary: string
  content: string
  author: string
  publishTime: string
  updateTime: string
  category: string
  tags: string[]
  coverImage?: string
  views: number
  status: 'draft' | 'published' | 'archived'
}

// 用户信息类型
export interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar?: string
  email: string
  phone?: string
  roles: string[]
  permissions: string[]
  lastLoginTime?: string
  isActive: boolean
}

// 登录表单类型
export interface LoginForm {
  username: string
  password: string
  remember?: boolean
  captcha?: string
}

// 菜单项类型
export interface MenuItem {
  id: string
  name: string
  path: string
  icon?: string
  children?: MenuItem[]
  roles?: string[]
  isExternal?: boolean
  isVisible: boolean
  sort: number
}

// 页面配置类型
export interface PageConfig {
  title: string
  description?: string
  keywords?: string[]
  showHeader: boolean
  showFooter: boolean
  layout: 'default' | 'fullscreen' | 'sidebar'
}

// HTTP 错误类型
export interface HttpError {
  code: number
  message: string
  details?: any
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message: string
}

// 组件属性基础类型
export interface BaseComponentProps {
  class?: string
  style?: string | Record<string, any>
}

// 事件处理器类型
export type EventHandler<T = Event> = (event: T) => void