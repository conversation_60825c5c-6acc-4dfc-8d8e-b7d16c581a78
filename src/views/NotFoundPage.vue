<template>
  <div class="not-found-page min-h-screen flex-center bg-gray-50">
    <div class="text-center max-w-md mx-auto px-4">
      <div class="mb-8">
        <div class="text-9xl font-bold text-blue-500 mb-4">404</div>
        <h1 class="text-2xl font-bold text-gray-900 mb-4">页面未找到</h1>
        <p class="text-gray-600 mb-8">
          抱歉，您访问的页面不存在或已被移动。
        </p>
      </div>
      
      <div class="space-y-4">
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link 
            to="/nanning"
            class="btn btn-primary"
          >
            <i class="i-ep-home-filled mr-2"></i>
            返回首页
          </router-link>
          <button 
            @click="goBack"
            class="btn btn-secondary"
          >
            <i class="i-ep-arrow-left mr-2"></i>
            返回上页
          </button>
        </div>
        
        <!-- 推荐链接 -->
        <div class="mt-12">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">您可能想要访问：</h2>
          <div class="space-y-2">
            <router-link 
              v-for="link in recommendedLinks" 
              :key="link.name"
              :to="link.path"
              class="block text-blue-600 hover:text-blue-700 hover:underline"
            >
              {{ link.name }}
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()

const recommendedLinks = ref([
  { name: '南宁门户首页', path: '/nanning' },
  { name: '新闻资讯', path: '/nanning/news' },
  { name: '政务服务', path: '/nanning/services' },
  { name: '营商服务', path: '/nanning/business' },
  { name: '民生服务', path: '/nanning/livelihood' }
])

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/nanning')
  }
}


</script>

<style scoped lang="scss">
.not-found-page {
  background-image: 
    radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
}
</style>