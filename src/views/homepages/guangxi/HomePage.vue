<template>
  <div class="guangxi-homepage">
    <div class="container-lg pt-220px">
      <div class="flex justify-between h-340px">
        <div class="w-256px bg-#fff">
          <Industry />
        </div>
        <div class="w-605px bg-#fff">
          中间区域
        </div>
        <div class="w-300px bg-#fff">
          右侧区域
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Industry from './component/industry.vue';
</script>

<style scoped lang="scss">
.guangxi-homepage {
  position: absolute;
  top: -200px;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://image.gxrc.com/thirdParty/gxjy/pc/home/<USER>
  background-size: 100%;
}

</style>