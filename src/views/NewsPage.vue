<template>
  <div class="news-page">
    <div class="container-lg py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">新闻资讯</h1>
        <p class="text-gray-600">了解{{ cityStore.cityInfo.name }}最新动态</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 主内容区 -->
        <div class="lg:col-span-3">
          <!-- 新闻列表 -->
          <div class="space-y-6">
            <div 
              v-for="article in newsList" 
              :key="article.id"
              class="news-card bg-white rounded-xl shadow-sm hover:shadow-md transition-all p-6"
            >
              <div class="flex gap-4">
                <img 
                  v-if="article.coverImage"
                  :src="article.coverImage" 
                  :alt="article.title"
                  class="w-32 h-24 object-cover rounded-lg flex-shrink-0"
                >
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <el-tag 
                      :type="getCategoryType(article.category)"
                      size="small"
                    >
                      {{ article.category }}
                    </el-tag>
                    <span class="text-sm text-gray-500">{{ article.publishTime }}</span>
                  </div>
                  <h2 class="text-xl font-semibold text-gray-900 mb-2 hover:text-blue-600 cursor-pointer transition-colors">
                    {{ article.title }}
                  </h2>
                  <p class="text-gray-600 text-sm leading-relaxed mb-3">{{ article.summary }}</p>
                  <div class="flex items-center justify-between text-sm text-gray-500">
                    <div class="flex items-center space-x-4">
                      <span>作者：{{ article.author }}</span>
                      <span>{{ article.views }} 浏览</span>
                    </div>
                    <div class="flex space-x-1">
                      <el-tag 
                        v-for="tag in article.tags.slice(0, 2)" 
                        :key="tag"
                        size="small"
                        effect="plain"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="flex justify-center mt-8">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="space-y-6">
          <!-- 分类筛选 -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="font-semibold text-gray-900 mb-4">新闻分类</h3>
            <div class="space-y-2">
              <div 
                v-for="category in categories" 
                :key="category.name"
                @click="filterByCategory(category.value)"
                class="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer transition-colors"
                :class="{ 'bg-blue-50 text-blue-600': selectedCategory === category.value }"
              >
                <span>{{ category.name }}</span>
                <span class="text-sm text-gray-500">{{ category.count }}</span>
              </div>
            </div>
          </div>

          <!-- 热门标签 -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="font-semibold text-gray-900 mb-4">热门标签</h3>
            <div class="flex flex-wrap gap-2">
              <el-tag 
                v-for="tag in hotTags" 
                :key="tag"
                @click="filterByTag(tag)"
                class="cursor-pointer hover:opacity-80 transition-opacity"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>

          <!-- 相关链接 -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="font-semibold text-gray-900 mb-4">相关链接</h3>
            <div class="space-y-2">
              <a 
                v-for="link in relatedLinks" 
                :key="link.name"
                :href="link.url"
                target="_blank"
                class="block text-blue-600 hover:text-blue-700 text-sm"
              >
                {{ link.name }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'
import type { NewsArticle } from '@/types'

const cityStore = useCityStore()

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
const selectedCategory = ref('')
const selectedTag = ref('')

// 新闻列表数据（模拟）
const newsList = ref<NewsArticle[]>([
  {
    id: '1',
    title: '南宁市2024年重点项目集中开工仪式举行',
    summary: '总投资超过500亿元的56个重点项目集中开工，涵盖基础设施、产业发展、民生改善等多个领域，为南宁经济社会发展注入新动力。',
    content: '',
    author: '南宁日报',
    publishTime: '2024-01-20',
    updateTime: '2024-01-20',
    category: '经济发展',
    tags: ['重点项目', '经济发展', '基础设施'],
    coverImage: '/images/news/project-launch.jpg',
    views: 8920,
    status: 'published'
  },
  // ... 更多新闻数据
])

// 分类数据
const categories = ref([
  { name: '全部', value: '', count: 100 },
  { name: '经济发展', value: 'economy', count: 25 },
  { name: '城市建设', value: 'construction', count: 20 },
  { name: '民生服务', value: 'livelihood', count: 18 },
  { name: '政策解读', value: 'policy', count: 15 },
  { name: '文化教育', value: 'culture', count: 12 },
  { name: '环境生态', value: 'environment', count: 10 }
])

// 热门标签
const hotTags = ref([
  '重点项目', '营商环境', '民生服务', '绿色发展', 
  '数字化', '东盟合作', '乡村振兴', '创新发展'
])

// 相关链接
const relatedLinks = computed(() => [
  { name: '南宁市人民政府', url: 'http://www.nanning.gov.cn' },
  { name: '广西新闻网', url: 'http://www.gxnews.com.cn' },
  { name: '南宁新闻网', url: 'http://www.nnnews.net' },
  { name: '南宁日报', url: 'http://www.nnrb.com.cn' }
])

// 获取分类类型样式
const getCategoryType = (category: string) => {
  const typeMap: Record<string, string> = {
    '经济发展': 'success',
    '城市建设': 'primary',
    '民生服务': 'warning',
    '政策解读': 'info',
    '文化教育': 'success',
    '环境生态': 'success'
  }
  return typeMap[category] || ''
}

// 分类筛选
const filterByCategory = (category: string) => {
  selectedCategory.value = category
  currentPage.value = 1
  // TODO: 实际项目中这里应该调用 API 获取数据
  console.log('Filter by category:', category)
}

// 标签筛选
const filterByTag = (tag: string) => {
  selectedTag.value = tag
  currentPage.value = 1
  // TODO: 实际项目中这里应该调用 API 获取数据
  console.log('Filter by tag:', tag)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  // TODO: 刷新数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // TODO: 刷新数据
}


</script>

<style scoped lang="scss">
.news-page {
  min-height: 100vh;
  background-color: #f8fafc;
}

.news-card {
  &:hover {
    transform: translateY(-2px);
  }
}

:deep(.el-pagination) {
  justify-content: center;
}
</style>