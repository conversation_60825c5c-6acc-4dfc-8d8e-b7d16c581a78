<template>
  <div id="app" class="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'


// 初始化 Store
const cityStore = useCityStore()


// 应用初始化
onMounted(() => {
  // 初始化城市 Store
  cityStore.initialize()
  
})


</script>

<style lang="scss">


.app {
  min-height: 100vh;
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  color: #333;
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 全局滚动条样式
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-300) var(--color-gray-100);
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--border-radius-full);
}

*::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--border-radius-full);
  
  &:hover {
    background: var(--color-gray-400);
  }
}


// 图片懒加载占位
img[loading="lazy"] {
  background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
  background-size: 400% 100%;
  animation: loading-placeholder 1.5s ease-in-out infinite;
}

@keyframes loading-placeholder {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: -100% 50%;
  }
}

// 减少动画 (用户偏好设置)
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  .btn {
    border-width: 2px !important;
  }
}
</style>
