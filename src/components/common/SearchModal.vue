<template>
  <el-dialog
    v-model="visible"
    title="搜索"
    width="600px"
    :modal="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    class="search-modal"
  >
    <div class="space-y-4">
      <!-- 搜索输入框 -->
      <div class="relative">
        <el-input
          v-model="searchQuery"
          placeholder="请输入搜索关键词..."
          size="large"
          :prefix-icon="Search"
          @input="handleSearch"
          @keyup.enter="performSearch"
          clearable
          class="search-input"
        />
      </div>

      <!-- 搜索建议 -->
      <div v-if="suggestions.length > 0" class="space-y-2">
        <div class="text-sm text-gray-600">搜索建议</div>
        <div class="space-y-1">
          <div 
            v-for="(suggestion, index) in suggestions" 
            :key="index"
            @click="selectSuggestion(suggestion)"
            class="p-2 hover:bg-gray-100 rounded cursor-pointer transition-colors text-sm"
          >
            <i class="i-ep-search mr-2 text-gray-400"></i>
            {{ suggestion }}
          </div>
        </div>
      </div>

      <!-- 热门搜索 -->
      <div class="space-y-2">
        <div class="text-sm text-gray-600">热门搜索</div>
        <div class="flex flex-wrap gap-2">
          <el-tag 
            v-for="tag in hotSearches" 
            :key="tag"
            @click="selectSuggestion(tag)"
            class="cursor-pointer hover:opacity-80 transition-opacity"
            size="small"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>

      <!-- 历史搜索 -->
      <div v-if="searchHistory.length > 0" class="space-y-2">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600">搜索历史</div>
          <button 
            @click="clearHistory"
            class="text-xs text-gray-400 hover:text-red-500 transition-colors"
          >
            清空历史
          </button>
        </div>
        <div class="space-y-1">
          <div 
            v-for="(history, index) in searchHistory" 
            :key="index"
            class="flex items-center justify-between p-2 hover:bg-gray-100 rounded transition-colors"
          >
            <div 
              @click="selectSuggestion(history)"
              class="flex-1 cursor-pointer text-sm"
            >
              <i class="i-ep-clock mr-2 text-gray-400"></i>
              {{ history }}
            </div>
            <button 
              @click="removeHistoryItem(index)"
              class="text-gray-400 hover:text-red-500 transition-colors ml-2"
            >
              <i class="i-ep-close text-xs"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <el-button @click="visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="performSearch"
          :disabled="!searchQuery.trim()"
        >
          搜索
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useCityStore } from '@/stores/cityStore'

// Props
const modelValue = defineModel<boolean>()

// Store
const cityStore = useCityStore()
const router = useRouter()

// 组件状态
const searchQuery = ref('')
const suggestions = ref<string[]>([])
const searchHistory = ref<string[]>([])

// 显示状态
const visible = computed({
  get: () => modelValue.value,
  set: (value) => {
    modelValue.value = value
    if (!value) {
      searchQuery.value = ''
      suggestions.value = []
    }
  }
})

// 热门搜索关键词
const hotSearches = computed(() => {
  const commonHot = ['政务服务', '办事指南', '政策文件', '便民服务']
  const citySpecific: Record<string, string[]> = {
    guangxi: ['北部湾', '西部陆海新通道', '中国-东盟', '乡村振兴'],
    gl: ['桂林山水', '漓江风景', '阳朔西街', '文旅服务'],
    lz: ['工业重镇', '汽车产业', '螺蛳粉', '制造业'],
    wz: ['两广枢纽', '粤港澳合作', '商贸物流', '岭南文化'],
    bs: ['革命老区', '红色旅游', '百色芒果', '脱贫攻坚'],
    qz: ['钦州港', '北部湾港', '中马产业园', '海洋经济'],
    hc: ['长寿之乡', '生态旅游', '健康养老', '民族文化'],
    bh: ['银滩', '海洋旅游', '海丝路', '滨海度假'],
    fg: ['边境口岸', '东兴口岸', '边贸合作', '跨境旅游'],
    yl: ['岭南都会', '商贸中心', '玉林牛巴', '特色农业'],
    cz: ['友谊关', '边贸口岸', '左江花山', '边境旅游'],
    gg: ['西江明珠', '内河港口', '绿色工业', '现代农业'],
    lb: ['世界瑶都', '瑶族文化', '生态旅游', '民族风情'],
    hz: ['粤港澳后花园', '康养旅游', '生态文明', '健康产业'],
    nn: ['南宁地铁', '邕江治理', '五象新区', '东盟博览会']
  }
  return [...commonHot, ...(citySpecific[cityStore.currentCity] || commonHot)]
})

// 搜索防抖
const debounceTimer = ref<NodeJS.Timeout>()

const handleSearch = () => {
  clearTimeout(debounceTimer.value)
  debounceTimer.value = setTimeout(() => {
    if (searchQuery.value.trim()) {
      generateSuggestions()
    } else {
      suggestions.value = []
    }
  }, 300)
}

// 生成搜索建议
const generateSuggestions = () => {
  const query = searchQuery.value.trim().toLowerCase()
  if (!query) {
    suggestions.value = []
    return
  }

  // 模拟搜索建议
  const allSuggestions = [
    ...hotSearches.value,
    '社保查询', '公积金查询', '税务服务', '教育服务',
    '医疗健康', '交通出行', '住房保障', '就业创业',
    '企业注册', '投资指南', '招商政策', '产业发展'
  ]

  suggestions.value = allSuggestions
    .filter(item => item.toLowerCase().includes(query))
    .slice(0, 6)
}

// 选择建议
const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  performSearch()
}

// 执行搜索
const performSearch = () => {
  const query = searchQuery.value.trim()
  if (!query) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  // 添加到搜索历史
  addToHistory(query)

  // 跳转到搜索结果页面
  router.push({
    path: `/${cityStore.currentCity}/search`,
    query: { q: query }
  })

  // 关闭模态框
  visible.value = false

  ElMessage.success(`正在搜索：${query}`)
}

// 添加到搜索历史
const addToHistory = (query: string) => {
  const history = [...searchHistory.value]
  const existingIndex = history.indexOf(query)
  
  if (existingIndex > -1) {
    history.splice(existingIndex, 1)
  }
  
  history.unshift(query)
  searchHistory.value = history.slice(0, 10) // 最多保存10条历史
  
  // 保存到 localStorage
  localStorage.setItem('search-history', JSON.stringify(searchHistory.value))
}

// 移除历史记录项
const removeHistoryItem = (index: number) => {
  searchHistory.value.splice(index, 1)
  localStorage.setItem('search-history', JSON.stringify(searchHistory.value))
}

// 清空搜索历史
const clearHistory = () => {
  searchHistory.value = []
  localStorage.removeItem('search-history')
}

// 加载搜索历史
onMounted(() => {
  const saved = localStorage.getItem('search-history')
  if (saved) {
    try {
      searchHistory.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  }
})

// 监听模态框打开，自动聚焦输入框
watch(visible, (newValue) => {
  if (newValue) {
    nextTick(() => {
      const input = document.querySelector('.search-input input') as HTMLInputElement
      input?.focus()
    })
  }
})
</script>

<style scoped lang="scss">
.search-modal {
  :deep(.el-dialog__header) {
    padding: 20px 24px 10px;
  }
  
  :deep(.el-dialog__body) {
    padding: 10px 24px 20px;
  }
}

.search-input {
  :deep(.el-input__wrapper) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>