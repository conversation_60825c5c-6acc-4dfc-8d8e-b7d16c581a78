<template>
  <div ref="element" class="count-up">{{ formattedValue }}</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  endValue: number
  startValue?: number
  duration?: number
  suffix?: string
  prefix?: string
}

const props = withDefaults(defineProps<Props>(), {
  startValue: 0,
  duration: 2000,
  suffix: '',
  prefix: ''
})

const displayValue = ref(props.startValue)
const animationFrame = ref<number>()

const animateCount = () => {
  if (isVisible.value) return // 防止重复动画
  
  const startTime = Date.now()
  const animate = () => {
    const currentTime = Date.now()
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / props.duration, 1)
    
    // 使用缓动函数
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3)
    const easedProgress = easeOutCubic(progress)
    
    const currentValue = props.startValue + (props.endValue - props.startValue) * easedProgress
    displayValue.value = Math.floor(currentValue)
    
    if (progress < 1) {
      animationFrame.value = requestAnimationFrame(animate)
    }
  }
  animate()
}

// 监听可见性，当元素可见时才开始动画
const element = ref<HTMLElement>()
const isVisible = ref(false)

onMounted(() => {
  if (!element.value) return
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isVisible.value) {
          isVisible.value = true
          animateCount()
        }
      })
    },
    { threshold: 0.1 }
  )
  
  observer.observe(element.value)
  
  onUnmounted(() => {
    if (animationFrame.value) {
      cancelAnimationFrame(animationFrame.value)
    }
    observer.disconnect()
  })
})

const formattedValue = computed(() => {
  return `${props.prefix}${displayValue.value.toLocaleString('zh-CN')}${props.suffix}`
})
</script>

<script lang="ts">
export default {
  name: 'CountUp'
}
</script>