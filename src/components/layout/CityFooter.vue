<template>
  <footer class="city-footer bg-#121315 text-white">
    <div class="container-lg py-8">
      <!-- 友情链接 -->
      <div class="text-center mb-6">
        
        <div class="flex justify-center flex-wrap gap-x-6 gap-y-2">
          <span class="text-sm text-gray-300">友情链接：</span>
          <a
            v-for="link in friendlyLinks"
            :key="link.name"
            :href="link.url"
            target="_blank"
            class="text-sm text-white hover:text-blue-300 transition-colors decoration-none"
          >
            {{ link.name }}
          </a>
        </div>
      </div>

      <!-- 版权信息和联系方式 -->
      <div class="text-center space-y-2 text-sm text-gray-300">
        <p>{{ copyright }}</p>
        <div class="space-x-4">
          <span>联系地址：{{ cityAddress }}</span>
          <span>联系电话：{{ cityPhone }}</span>
          <span>邮箱地址：{{ cityEmail }}</span>
        </div>
        <div class="space-x-4">
          <span>{{ icp }}</span>
          <span>{{ policeRecord }}</span>
        </div>
      </div>

      <!-- 备案图标 -->
      <div class="flex justify-center items-center gap-4 mt-4">
        <!-- <img src="/images/beian-icon1.png" alt="备案图标" class="h-5">
        <img src="/images/beian-icon2.png" alt="备案图标" class="h-5">
        <img src="/images/beian-icon3.png" alt="备案图标" class="h-5">
        <img src="/images/beian-icon4.png" alt="备案图标" class="h-5">
        <img src="/images/beian-icon5.png" alt="备案图标" class="h-5"> -->
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCityStore } from '@/stores/cityStore'

// Store
const cityStore = useCityStore()

// 动态计算当前年份
const currentYear = new Date().getFullYear()

// 版权信息
const copyright = computed(() => `广西壮族自治区人民政府门户网站 版权所有 COPYRIGHT 2021`)

// 根据城市配置动态内容
const cityAddress = computed(() => {
  const cityConfig: Record<string, string> = {
    nn: '广西壮族自治区南宁市青秀区民族大道112号',
    guangxi: '广西壮族自治区南宁市青秀区民族大道112号',
    gl: '广西壮族自治区桂林市象山区中山南路69号',
    lz: '广西壮族自治区柳州市城中区文昌路66号',
    wz: '广西壮族自治区梧州市万秀区西江三路19号',
    bs: '广西壮族自治区百色市右江区向阳路19号',
    qz: '广西壮族自治区钦州市钦南区永福东大街1号',
    hc: '广西壮族自治区河池市金城江区新建路76号',
    bh: '广西壮族自治区北海市海城区北京路78号',
    fg: '广西壮族自治区防城港市港口区渔万路1号',
    yl: '广西壮族自治区玉林市玉州区一环路1号',
    cz: '广西壮族自治区崇左市江州区石景林路1号',
    gg: '广西壮族自治区贵港市港北区金港大道1066号',
    lb: '广西壮族自治区来宾市兴宾区前卫路1号',
    hz: '广西壮族自治区贺州市八步区贺州大道1号'
  }
  return cityConfig[cityStore.currentCity] || '广西壮族自治区南宁市青秀区民族大道112号'
})

const cityPhone = computed(() => {
  const phoneConfig: Record<string, string> = {
    nn: '南宁热线：0771-12345',
    guangxi: '广西热线：0771-12345',
    gl: '桂林热线：0773-12345',
    lz: '柳州热线：0772-12345',
    wz: '梧州热线：0774-12345',
    bs: '百色热线：0776-12345',
    qz: '钦州热线：0777-12345',
    hc: '河池热线：0778-12345',
    bh: '北海热线：0779-12345',
    fg: '防城港热线：0770-12345',
    yl: '玉林热线：0775-12345',
    cz: '崇左热线：0771-12346',
    gg: '贵港热线：0775-12346',
    lb: '来宾热线：0772-12346',
    hz: '贺州热线：0774-12346'
  }
  return phoneConfig[cityStore.currentCity] || '广西热线：0771-12345'
})

const cityEmail = computed(() => {
  const emailConfig: Record<string, string> = {
    nn: '南宁市政府办公厅@广西数字人才网',
    guangxi: '广西壮族自治区政府办公厅@广西数字人才网',
    gl: '桂林市政府办公厅@广西数字人才网',
    lz: '柳州市政府办公厅@广西数字人才网',
    wz: '梧州市政府办公厅@广西数字人才网',
    bs: '百色市政府办公厅@广西数字人才网',
    qz: '钦州市政府办公厅@广西数字人才网',
    hc: '河池市政府办公厅@广西数字人才网',
    bh: '北海市政府办公厅@广西数字人才网',
    fg: '防城港市政府办公厅@广西数字人才网',
    yl: '玉林市政府办公厅@广西数字人才网',
    cz: '崇左市政府办公厅@广西数字人才网',
    gg: '贵港市政府办公厅@广西数字人才网',
    lb: '来宾市政府办公厅@广西数字人才网',
    hz: '贺州市政府办公厅@广西数字人才网'
  }
  return emailConfig[cityStore.currentCity] || '广西壮族自治区政府办公厅@广西数字人才网'
})

// ICP备案信息
const icp = computed(() => '桂ICP备05000416号')
const policeRecord = computed(() => '网上政务大厅')

// 友情链接
const friendlyLinks = computed(() => [
  { name: '中华人民共和国中央人民政府', url: 'http://www.gov.cn' },
  { name: '国务院部门', url: 'http://www.gov.cn/guowuyuan/2005-05/25/content_1006.htm' },
  { name: '广西壮族自治区人民政府', url: 'http://www.gxzf.gov.cn' },
  { name: '数字人才网', url: 'http://www.digitaltalent.cn' },
  { name: '网上政务大厅', url: 'http://www.guangxi.gov.cn' }
])
</script>

<style scoped lang="scss">
.city-footer {
  background: #222222;
  
  .bg-dark {
    background-color: #222222 !important;
  }
}
</style>
