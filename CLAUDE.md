# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development Workflow
```bash
# Install dependencies (uses pnpm)
pnpm install

# Start development server on port 3000
pnpm dev

# Build for production (includes TypeScript compilation)
pnpm build

# Preview production build
pnpm preview

# Run tests with Vitest
pnpm test
pnpm test:ui  # Test with UI interface

# Code quality checks
pnpm lint           # ESLint check
pnpm lint:fix       # ESLint auto-fix
pnpm type-check     # TypeScript type checking
```

### Package Manager
This project uses **pnpm** (version 10.14.0+) as specified in package.json. Always use pnpm for dependency management.

## Architecture Overview

### Multi-City Portal System
This is a Vue 3 application for Guangxi provincial and city-level government portals with dynamic city switching:

- **Root Route (`/`)**: Guangxi province homepage (default)
- **City Routes (`/:city`)**: Individual city portals (e.g., `/nn` for Nanning, `/gl` for Guilin)
- **Dynamic Components**: Each city has its own homepage component at `src/views/homepages/{cityCode}/HomePage.vue`

### Key Architectural Patterns

#### 1. City-Based Routing System
- Routes are dynamically generated based on city configuration in `cityStore.ts`
- City codes: `guangxi`, `nn` (Nanning), `gl` (Guilin), `lz` (Liuzhou), etc.
- City homepage components are lazy-loaded: `src/views/homepages/{cityCode}/HomePage.vue`
- Fallback to `DefaultHomePage.vue` if city component doesn't exist

#### 2. State Management (Pinia)
- **cityStore**: Manages current city, theme, configuration, and routing logic
- Persistent storage with localStorage for city selection
- Theme variables are dynamically updated based on current city

#### 3. Theme System
- CSS custom properties updated dynamically via `cityStore.updateThemeColors()`
- UnoCSS for atomic CSS with city-specific color schemes
- SCSS for complex styling and Element Plus overrides

#### 4. Component Structure
```
src/components/
├── common/          # Reusable components (CitySelector, CountUp, SearchModal)
└── layout/          # Layout components (HeaderComponent, CityFooter)
```

### Tech Stack Integration

#### Auto-Import Configuration
- Vue APIs, VueRouter, Pinia automatically imported
- Element Plus components automatically imported and registered
- Type definitions generated in `auto-imports.d.ts` and `components.d.ts`

#### Build Configuration (Vite)
- Path aliases: `@/` maps to `src/`
- Development server on port 3000 with auto-open
- API proxy to `localhost:8080` for `/api` routes
- Code splitting and chunk optimization configured

#### TypeScript Configuration
- Strict mode enabled with comprehensive type checking
- Path mapping for clean imports
- Vue SFC support with `<script setup lang="ts">`

## Development Patterns

### Adding New Cities
1. Add city configuration to `CITIES_CONFIG` in `src/stores/cityStore.ts`
2. Create homepage component: `src/views/homepages/{cityCode}/HomePage.vue`
3. Update route pattern in `src/router/index.ts` if needed
4. Add city-specific themes and assets

### Component Development
- Use `<script setup lang="ts">` syntax
- Import Element Plus components automatically (configured in Vite)
- Use UnoCSS classes for styling with SCSS for complex logic
- Follow PascalCase for component names, kebab-case for files

### API Integration
- Base HTTP client in `src/services/api.ts`
- Axios utilities with interceptors in `src/utils/axios-utils.ts`
- API endpoints should be added to `src/services/` directory

### Testing
- Use Vitest for unit/component tests
- Vue Test Utils for component testing
- Place tests adjacent to components or in `__tests__` directories

## Important File Locations

### Configuration Files
- `vite.config.ts` - Build configuration, plugins, aliases
- `uno.config.ts` - UnoCSS configuration with shortcuts and theme
- `tsconfig.json` - TypeScript configuration with path mapping

### Core Application Files
- `src/main.ts` - Application entry point
- `src/App.vue` - Root component
- `src/router/index.ts` - Router configuration with city-based routing
- `src/stores/cityStore.ts` - City state management and configuration
- `src/layout/AppLayout.vue` - Main layout component

### City-Specific Components
- `src/views/homepages/guangxi/` - Guangxi province homepage
- `src/views/homepages/nanning/` - Nanning city homepage
- `src/views/homepages/DefaultHomePage.vue` - Fallback homepage

## Performance Considerations

- All route components are lazy-loaded
- Element Plus components are imported on-demand
- UnoCSS generates only used CSS classes
- Code splitting configured for optimal bundle sizes
- City themes are cached and applied dynamically

## Code Quality Standards

- TypeScript strict mode enforced
- ESLint configuration for Vue 3 and TypeScript
- Consistent import order and naming conventions
- Path aliases used for clean imports (`@/` instead of relative paths)