<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="广西门户网站" />
    <meta name="keywords" content="广西,南宁,门户,政府,服务" />
    <title>广西门户网站</title>
    <style>
      /* Critical minimal styles to avoid flash of white */
      html, body, #app { height: 100%; }
      body { margin: 0; background: #f0f6ff; }
      #app { background: transparent; }
      /* Place a minimal loading layer (hidden by default, will be toggled by router) */
      #app-loading { display: none; position: fixed; inset: 0; background: #fff; z-index: 9999; }
    </style>
    <!-- Preload homepage background image -->
    <link rel="preload" as="image" href="https://image.gxrc.com/thirdParty/gxjy/pc/home/<USER>" />
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>